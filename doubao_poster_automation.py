"""
豆包AI海报生成自动化脚本
使用Selenium控制Edge浏览器自动化操作
"""

import time
from selenium import webdriver
from selenium.webdriver.edge.service import Service
from selenium.webdriver.edge.options import Options
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import TimeoutException, WebDriverException
from webdriver_manager.microsoft import EdgeChromiumDriverManager


class DoubaoAutomation:
    def __init__(self):
        """初始化自动化类"""
        self.driver = None
        self.wait = None

    def setup_driver(self):
        """设置Edge浏览器驱动"""
        try:
            print("正在配置Edge浏览器...")
            # 配置Edge浏览器选项
            edge_options = Options()
            # 可以根据需要添加其他选项
            # edge_options.add_argument("--headless")  # 无头模式，如果需要的话
            edge_options.add_argument("--disable-blink-features=AutomationControlled")
            edge_options.add_experimental_option("excludeSwitches", ["enable-automation"])
            edge_options.add_experimental_option('useAutomationExtension', False)

            # 尝试使用webdriver-manager自动管理Edge驱动
            try:
                print("尝试自动下载Edge WebDriver...")
                service = Service(EdgeChromiumDriverManager().install())
                self.driver = webdriver.Edge(service=service, options=edge_options)
            except Exception as e:
                print(f"自动下载失败: {e}")
                print("尝试使用系统默认Edge驱动...")
                # 如果自动下载失败，尝试使用系统默认驱动
                self.driver = webdriver.Edge(options=edge_options)

            self.driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")

            # 设置等待时间
            self.wait = WebDriverWait(self.driver, 10)

            # 最大化窗口
            self.driver.maximize_window()

            print("Edge浏览器启动成功！")
            return True

        except WebDriverException as e:
            print(f"启动Edge浏览器失败: {e}")
            print("请确保已安装Edge浏览器")
            print("如果问题持续，请手动下载Edge WebDriver并放置在系统PATH中")
            return False
        except Exception as e:
            print(f"设置驱动时发生错误: {e}")
            return False
    
    def open_doubao_website(self):
        """打开豆包AI网站"""
        try:
            print("正在打开豆包AI网站...")
            self.driver.get("https://www.doubao.com")
            
            # 等待页面加载完成
            time.sleep(3)
            
            print("豆包AI网站已成功打开！")
            print(f"当前页面标题: {self.driver.title}")
            print(f"当前页面URL: {self.driver.current_url}")
            
            return True
            
        except Exception as e:
            print(f"打开豆包AI网站失败: {e}")
            return False
    
    def wait_for_user_input(self):
        """等待用户输入下一步操作"""
        print("\n" + "="*50)
        print("第一步完成！Edge浏览器已打开豆包AI网站")
        print("请告诉我接下来需要执行的操作...")
        print("="*50)
        
        # 保持浏览器打开，等待用户指示
        input("按Enter键继续或Ctrl+C退出...")
    
    def close_browser(self):
        """关闭浏览器"""
        if self.driver:
            self.driver.quit()
            print("浏览器已关闭")


def main():
    """主函数"""
    print("开始启动豆包AI自动化脚本...")
    automation = DoubaoAutomation()

    try:
        print("正在初始化浏览器...")
        # 设置浏览器驱动
        if not automation.setup_driver():
            print("浏览器初始化失败，程序退出")
            return

        print("浏览器初始化成功，正在访问豆包AI网站...")
        # 打开豆包AI网站
        if not automation.open_doubao_website():
            print("访问豆包AI网站失败，程序退出")
            return

        # 等待用户输入下一步操作
        automation.wait_for_user_input()

    except KeyboardInterrupt:
        print("\n用户中断操作")
    except Exception as e:
        print(f"发生错误: {e}")
        import traceback
        traceback.print_exc()
    finally:
        automation.close_browser()


if __name__ == "__main__":
    main()
