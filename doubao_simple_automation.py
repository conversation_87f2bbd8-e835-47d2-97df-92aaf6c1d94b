"""
豆包AI海报生成简化自动化脚本
使用webbrowser模块打开浏览器
"""

import webbrowser
import time
import os


class DoubaoSimpleAutomation:
    def __init__(self):
        """初始化自动化类"""
        self.url = "https://www.doubao.com"
        
    def open_doubao_website(self):
        """使用默认浏览器打开豆包AI网站"""
        try:
            print("正在打开豆包AI网站...")
            print(f"目标网址: {self.url}")
            
            # 使用默认浏览器打开网站
            webbrowser.open(self.url)
            
            print("豆包AI网站已在默认浏览器中打开！")
            print("请在浏览器中查看网站是否正确加载")
            
            return True
            
        except Exception as e:
            print(f"打开豆包AI网站失败: {e}")
            return False
    
    def open_edge_browser(self):
        """尝试使用Edge浏览器打开网站"""
        try:
            print("正在尝试使用Edge浏览器打开豆包AI网站...")
            
            # Windows系统中Edge浏览器的常见路径
            edge_paths = [
                r"C:\Program Files (x86)\Microsoft\Edge\Application\msedge.exe",
                r"C:\Program Files\Microsoft\Edge\Application\msedge.exe",
            ]
            
            edge_path = None
            for path in edge_paths:
                if os.path.exists(path):
                    edge_path = path
                    break
            
            if edge_path:
                print(f"找到Edge浏览器: {edge_path}")
                # 注册Edge浏览器
                webbrowser.register('edge', None, webbrowser.BackgroundBrowser(edge_path))
                # 使用Edge打开网站
                webbrowser.get('edge').open(self.url)
                print("豆包AI网站已在Edge浏览器中打开！")
                return True
            else:
                print("未找到Edge浏览器，使用默认浏览器...")
                return self.open_doubao_website()
                
        except Exception as e:
            print(f"使用Edge浏览器失败: {e}")
            print("尝试使用默认浏览器...")
            return self.open_doubao_website()
    
    def wait_for_user_input(self):
        """等待用户输入下一步操作"""
        print("\n" + "="*50)
        print("第一步完成！浏览器已打开豆包AI网站")
        print("请在浏览器中确认网站已正确加载")
        print("然后告诉我接下来需要执行的操作...")
        print("="*50)
        
        # 等待用户指示
        input("按Enter键继续或Ctrl+C退出...")


def main():
    """主函数"""
    print("开始启动豆包AI简化自动化脚本...")
    automation = DoubaoSimpleAutomation()
    
    try:
        print("正在打开浏览器...")
        
        # 尝试使用Edge浏览器，如果失败则使用默认浏览器
        if not automation.open_edge_browser():
            print("所有浏览器打开尝试都失败了")
            return
        
        # 等待用户输入下一步操作
        automation.wait_for_user_input()
        
    except KeyboardInterrupt:
        print("\n用户中断操作")
    except Exception as e:
        print(f"发生错误: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
